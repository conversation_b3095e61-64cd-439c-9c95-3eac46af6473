import React, { useState } from 'react';
import {
  <PERSON>ard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CButton,
  CFormInput,
  CPagination,
  CPaginationItem,CModal,CModalHeader,CModalTitle,
  CModalBody,CModalFooter,CFormLabel,
  CDropdown,
  CDropdownMenu,
  CDropdownItem,
  CDropdownToggle,
} from '@coreui/react';
import { Link } from 'react-router-dom';

import CIcon from '@coreui/icons-react';
import * as icon from '@coreui/icons';
import { FaFilePdf } from "react-icons/fa6";
import { IoAddCircleSharp } from "react-icons/io5";

import useFetch from '../../../hooks/useFetch';
import { api_path } from '../../../../environments';
import { toast } from 'react-toastify';
import axios from 'axios';
import { useAuth } from '../../../hooks/AuthContext'
import { DatePicker , Tooltip } from 'antd';
import { useMediaQuery } from 'react-responsive';
import dayjs from 'dayjs';


const ViewSlowPayments = () => {
  const { role } = useAuth()
  const isAdmin = role == 'admin';
  const isMobile = useMediaQuery({ query: '(max-width:767px)' })

  const [paymentID,setPaymentID] = useState('')
  const [Evisible,setEVisible] = useState(false)
  const [amount,setAmount] = useState(false)
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10000;
  const [selectedDate, setSelectedDate] = useState();

  const { data: allSlow , loading , error} = useFetch('other/allSlow');

//  console.log(allSlow)
  const initialCashRecords = Array.isArray(allSlow)
  ? allSlow.map(payment => ({
      id: payment.SlowPaymentID,
      customer: payment.CustomerName,
      phone: payment.NeededPhone,
      price: payment.SellingPrice,
      agent: payment.UserName,
      remained: payment.RemainedCost,
      needed: payment.NeededAmount,
      isCompleted: payment.isCompleted,
      date: payment.created_at
  }))
  : [];


  const filteredStaffs = initialCashRecords.filter(staff => {
    // Text search filter
    const matchesSearch = !searchTerm ||
      (staff.customer && staff.customer.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (staff.phone && staff.phone.toLowerCase().includes(searchTerm.toLowerCase()));

    // Date filter
    const matchesDate = !selectedDate ||
      (staff.date && dayjs(staff.date).format('YYYY-MM-DD') === selectedDate);

    return matchesSearch && matchesDate;
  });

  const totalPages = Math.ceil(filteredStaffs.length / itemsPerPage);
  
  const indexOfLastStaff = currentPage * itemsPerPage;
  const indexOfFirstStaff = indexOfLastStaff - itemsPerPage;
  const currentStaffs = filteredStaffs.slice(indexOfFirstStaff, indexOfLastStaff);

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };


  const handleDeleteCash =async (cashID) => {
    const response = await axios.post(`${api_path}/api/other/deleteSlowRecord/${cashID}`);

    if(response.status == 200){
      toast.success("User Deleted successfully");
      window.location.reload();
    }
  }

  const handleAddPayment =async (paymentID) => {
    const token = localStorage.getItem('joh_token');

    if (!token) {
      throw new Error('No token found');
    }
    
    const data = {
        paymentID,
        amount
    }
    const response = await axios.post(`${api_path}/api/other/addPaymentItem`,data, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

    if(response.status == 200){
      toast.success("Payment added successfully");
      window.location.reload();
    }
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'Tsh' }).format(value);
  }

  const onDateChange = (date, dateString) => {
    setSelectedDate(dateString);
  };

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error loading users: {error.message}</div>;

  return (
    <>
    <div className="d-flex justify-content-between mb-2">
        <div className=""></div>
        <div
          className={`${isMobile ? 'flex-column' : 'd-flex'}`}
          style={{ display: 'flex' }}
        >
          <div className={`d-flex ${isMobile ? 'mb-3 justify-content-end' : 'flex-row'}`}>

            <Link to="/add-slow-payment" className="mx-3">
              <Tooltip title="Add New Record">
                <CButton color="primary">
                  <IoAddCircleSharp />
                </CButton>
              </Tooltip>
            </Link>

          </div>

          {/* Date filter */}
          <div className="d-flex gap-2 align-items-center">
             <DatePicker
                placeholder="Filter by date"
                onChange={onDateChange}
                value={selectedDate ? dayjs(selectedDate) : null}
                allowClear
              />
              {selectedDate && (
                <CButton
                  color="secondary"
                  size="sm"
                  onClick={() => setSelectedDate(null)}
                  title="Clear date filter"
                >
                  Clear
                </CButton>
              )}
          </div>
        </div>
      </div>
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <div className="d-flex justify-content-between align-items-center">
              <strong>All Slow Payments Records</strong>
              <small className="text-muted">
                Showing {filteredStaffs.length} of {initialCashRecords.length} records
                {selectedDate && ` (filtered by date: ${selectedDate})`}
              </small>
            </div>
            <div className='d-flex gap-2'>
              <CFormInput
                type="text"
                placeholder="Search by customer or phone..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="mt-2"
              />
            </div>
          </CCardHeader>
          <CCardBody>
            <CTable responsive>
              <CTableHead>
                <CTableRow>
                  <CTableHeaderCell scope="col">#</CTableHeaderCell>
                  <CTableHeaderCell scope="col">customer</CTableHeaderCell>
                  <CTableHeaderCell scope="col">phone</CTableHeaderCell>
                  <CTableHeaderCell scope="col">Anachotakiwa</CTableHeaderCell>
                  <CTableHeaderCell scope="col">Kilichobaki</CTableHeaderCell>
                  {/* {isAdmin && (
                    <> */}
                    <CTableHeaderCell scope="col">Action</CTableHeaderCell>
                    {/* </>
                  )} */}
                </CTableRow>
              </CTableHead>
              <CTableBody>
                {currentStaffs.map((cash,index) => (
                  <CTableRow key={cash.id}>
                    <CTableHeaderCell scope="row">{index + 1}</CTableHeaderCell>
                    <CTableDataCell>{cash.customer}</CTableDataCell>
                    <CTableDataCell>{cash.phone}</CTableDataCell>

                    <CTableDataCell>{formatCurrency(cash.needed)}</CTableDataCell>
                    <CTableDataCell>{formatCurrency(cash.remained)}</CTableDataCell>

                      <CTableDataCell className='d-flex gap-2'>

                      {cash.isCompleted == 1 ? (
                          <p className='text-success'>100%</p>
                      ) : cash.remained == 0 ? (
                          <CDropdown alignment="end">
                            <CDropdownToggle color="transparent" caret={false} className="text-white p-0">
                                <CButton color="success" className='text-white'>
                                    <CIcon icon={icon.cilArrowCircleBottom} />
                                </CButton>
                            </CDropdownToggle>
                            <CDropdownMenu>
                              <Link to={'/add-cash?PaymentID=' + cash.id}>
                                <CDropdownItem>Smart Phone</CDropdownItem>
                              </Link>
                              <Link to={'/add-cash-small?PaymentID=' + cash.id}>
                                <CDropdownItem>Simu Ndogo</CDropdownItem>
                              </Link>
                            </CDropdownMenu>
                          </CDropdown>
                      ) : (
                          <Link>
                              <CButton color="secondary" 
                              onClick={() =>{
                                  setEVisible(!Evisible);
                                  setPaymentID(cash.id)
                              }}>
                                  <CIcon icon={icon.cilPlus} />
                              </CButton>
                          </Link>
                      )}

                        
                            <Link to={'/single-payment?id=' + cash.id}>
                            <CButton color="secondary">
                              <CIcon icon={icon.cilChevronDoubleRight} />
                            </CButton>
                            </Link>
                          {isAdmin && (
                            <>
                            
                              <Link to={'/add-slow-payment?id=' + cash.id}>
                              <CButton color="primary">
                                <CIcon icon={icon.cilPenAlt} />
                              </CButton>
                              </Link>
                              <CButton 
                                  color="danger" 
                                  className='text-white' 
                                  onClick={() => {
                                    const isConfirmed = window.confirm("Are you sure you want to delete this record?");
                                    
                                    if (isConfirmed) {
                                      handleDeleteCash(cash.id);
                                    }
                                  }}
                                  
                                  >                        
                                  <CIcon icon={icon.cilTrash} />
                                  </CButton>
                                  </>
                        )}
                      </CTableDataCell>
                  </CTableRow>
                ))}
              </CTableBody>
            </CTable>
            <CPagination aria-label="Page navigation example" align="end">
              <CPaginationItem
                aria-label="Previous"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                <span aria-hidden="true">&laquo;</span>
              </CPaginationItem>
              {Array.from({ length: totalPages }, (_, index) => (
                <CPaginationItem
                  key={index + 1}
                  active={index + 1 === currentPage}
                  onClick={() => handlePageChange(index + 1)}
                >
                  {index + 1}
                </CPaginationItem>
              ))}
              <CPaginationItem
                aria-label="Next"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                <span aria-hidden="true">&raquo;</span>
              </CPaginationItem>
            </CPagination>
          </CCardBody>
        </CCard>
      </CCol>

      <CModal backdrop="static" visible={Evisible} onClose={() => setEVisible(false)}>
        <CModalHeader>
        <CModalTitle>Add Payment</CModalTitle>
        </CModalHeader>
        <CModalBody>
            <CRow className="mb-3">
              <CFormLabel htmlFor="name" className="col-sm-2 col-form-label">
                Kiasi:
              </CFormLabel>
              <div className="col-sm-10">
                <CFormInput
                  type="number"
                  id="name"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                />
              </div>
            </CRow>
        </CModalBody>
        <CModalFooter>
        <CButton color="secondary" onClick={() => setEVisible(false)}>
            Close
        </CButton>
        <CButton color="primary" 
        onClick={() => handleAddPayment(paymentID)}
        >Save changes</CButton>
        </CModalFooter>
        </CModal>
    </CRow>
    
  </>
  );
};

export default ViewSlowPayments;
