import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { api_path } from '../../../../environments';
import { toast } from 'react-toastify';
import { useLocation , useNavigate} from 'react-router-dom';
import useFetch from '../../../hooks/useFetch';
import { Select } from 'antd';
import { PageHeader } from '../../../components/ModernBreadcrumb';


const AddCashSmall = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const id = queryParams.get('id');
  const paymentID = queryParams.get('PaymentID');

  const isEdit = id !== null;

  const navigate = useNavigate()

  const { data: cash, loading, error: cashError } = useFetch(isEdit ? 'other/singleSmallCash/' + id : '');  
  const { data: payment } = useFetch(paymentID ? 'other/singleSlow/' + paymentID : '');
  const { data: allPhone , loading : phoneLoading } = useFetch('other/allSmallPhone');

  const [customer, setCustomer] = useState('');
  const [phone, setPhone] = useState('');
  const [PhoneID, setPhoneID] = useState('');
 const [phoneCost, setPhoneCost] = useState('');

  useEffect(() => {
    if (cash) {
      setCustomer(cash.CustomerName);
      setPhone(cash.CustomerPhoneNumber);
      setPhoneID(cash.SmallPhoneID);
      setPhoneCost(cash.SellingPrice);
    }
  }, [cash]);

  const structuredPhones = allPhone?.map(phone => ({
    value: phone.SmallPhoneID,  
    label: phone.PhoneName,
  }));

  useEffect(() => {
    if (payment) {
      setCustomer(payment.CustomerName);
      setPhoneCost(payment.NeededAmount);
      setPhone(payment.PhoneNumber);
    }
  }, [payment]);

  const [error, setError] = useState('');

  const handleAddCash = async (e) => {
    e.preventDefault();

    setError('');

    if (!customer || !phone || !phoneCost) {
      setError('Please fill all required fields.');
      return;
    }

    const token = localStorage.getItem('joh_token');

    if (!token) {
      throw new Error('No token found');
    }

    const cashData = {
      customer,
      phone,
      PhoneID,
      phoneCost,
      paymentID
    };


    try {
      let response;

      // If we're editing, use PUT request, otherwise use POST
      if (isEdit) {
        response = await axios.put(`${api_path}/api/other/updateSmallCash/${id}`, cashData, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
      } else {
        response = await axios.post(`${api_path}/api/other/addSmallCash`, cashData, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
      }

      if (response.status === 200) {
        toast.success(`${isEdit ? 'Cash Updated' : 'Cash Added'} successfully`);
        setCustomer('');
        setPhone('');
        setPhoneCost('');
        setPhoneID('');
        navigate('/view-cash-small');
      }

      if (response.status === 400) {
        toast.error(response.message);
        setError(response.message )
        setCustomer('');
        setPhone('');
        setPhoneCost('');
        setPhoneID('');
        navigate('/view-cash-small');
      }
    } catch (error) {
      console.error(error);

      if (error.response) {
        setError(error.response.data.message || 'An error occurred. Please try again.');
      } else if (error.request) {
        setError('Network error. Please check your connection.');
      } else {
        setError('An unexpected error occurred.');
      }

      toast.error(error.message || 'Failed to add cash');
    }
  };

  if (cashError) return (
    <div className="modern-alert alert-error">
      <strong>Error:</strong> Failed to load cash data - {cashError.message}
    </div>
  );

  if (loading || phoneLoading) return (
    <div style={{ maxWidth: '800px', margin: '0 auto' }}>
      <div className="modern-card">
        <div className="modern-card-body">
          <div className="text-center py-8">
            <div className="loading-spinner" style={{ width: '40px', height: '40px', margin: '0 auto 16px' }}></div>
            <h5 className="text-gray-600 mb-2">Loading...</h5>
            <p className="text-gray-500">
              {loading ? 'Loading cash record data...' : 'Loading available phones...'}
            </p>
          </div>

          {/* Skeleton Loading */}
          <div className="mt-6">
            <div className="skeleton-loader" style={{ height: '20px', marginBottom: '16px' }}></div>
            <div className="skeleton-loader" style={{ height: '48px', marginBottom: '24px' }}></div>
            <div className="skeleton-loader" style={{ height: '48px', marginBottom: '24px' }}></div>
            <div className="skeleton-loader" style={{ height: '48px', marginBottom: '24px' }}></div>
            <div className="skeleton-loader" style={{ height: '48px', marginBottom: '24px' }}></div>
          </div>
        </div>
      </div>
    </div>
  );

  const handleSelectChange = (value) => {
    setPhoneID(value);
  };

  return (
    <div style={{ maxWidth: '800px', margin: '0 auto' }}>
      {/* Modern Header */}
      <PageHeader
        title={`${isEdit ? 'Edit' : 'Add'} Basic Phone Sale`}
        subtitle={isEdit ? 'Update the basic phone sale details' : 'Create a new basic phone sale record'}
        breadcrumbs={[
          { title: 'Dashboard', path: '/', icon: '🏠' },
          { title: 'Basic Sales', path: '/view-cash-small', icon: '📞' },
          { title: isEdit ? 'Edit Sale' : 'New Sale', path: '/add-cash-small', isActive: true }
        ]}
      />

      {/* Modern Form Card */}
      <div className="modern-card">
        <div className="modern-card-body">
          <form onSubmit={handleAddCash}>
            {error && (
              <div className="modern-alert alert-error mb-6">
                <strong>Error:</strong> {error}
              </div>
            )}

            <div className="row">
              <div className="col-md-6">
                <div className="modern-form-group">
                  <label className="modern-form-label">
                    👤 Customer Name
                  </label>
                  <input
                    type="text"
                    className="modern-form-input"
                    placeholder="Enter customer name"
                    value={customer}
                    onChange={(e) => setCustomer(e.target.value)}
                    required
                  />
                </div>
              </div>

              <div className="col-md-6">
                <div className="modern-form-group">
                  <label className="modern-form-label">
                    📱 Phone Number
                  </label>
                  <input
                    type="tel"
                    className="modern-form-input"
                    placeholder="Enter phone number"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    required
                  />
                </div>
              </div>
            </div>

            <div className="modern-form-group">
              <label className="modern-form-label">
                📞 Select Basic Phone
              </label>
              <Select
                showSearch
                placeholder="🔍 Search and select a basic phone"
                style={{
                  width: '100%',
                  height: '48px'
                }}
                size="large"
                filterOption={(input, option) =>
                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                }
                options={structuredPhones}
                onChange={handleSelectChange}
                value={PhoneID || undefined}
                notFoundContent={
                  phoneLoading ? (
                    <div className="text-center py-3">
                      <div className="loading-spinner"></div>
                      <span className="ml-2">Loading phones...</span>
                    </div>
                  ) : (
                    <div className="text-center py-3 text-gray-500">
                      No basic phones found
                    </div>
                  )
                }
              />
            </div>

            <div className="modern-form-group">
              <label className="modern-form-label">
                💰 Phone Price (TSH)
              </label>
              <input
                type="number"
                className="modern-form-input"
                placeholder="Enter phone price"
                value={phoneCost}
                onChange={(e) => setPhoneCost(e.target.value)}
                min="0"
                step="1000"
                required
              />
              <small className="text-gray-500 mt-1 d-block">
                Enter the selling price in Tanzanian Shillings
              </small>
            </div>

            {/* Action Buttons */}
            <div className="d-flex gap-3 justify-content-end mt-6">
              <button
                type="button"
                className="btn-modern btn-secondary-modern"
                onClick={() => window.history.back()}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="btn-modern btn-primary-modern"
                disabled={!customer || !phone || !PhoneID || !phoneCost}
              >
                {isEdit ? '✅ Update Sale' : '📞 Complete Sale'}
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Help Section */}
      <div className="modern-card mt-4" style={{ background: 'var(--info-50)' }}>
        <div className="modern-card-body">
          <h5 className="text-info-600 mb-3">💡 Quick Tips</h5>
          <ul className="text-sm text-gray-600 mb-0" style={{ paddingLeft: '20px' }}>
            <li>Make sure to select the correct basic phone from the dropdown</li>
            <li>Double-check the customer information before submitting</li>
            <li>The price should be the final selling amount</li>
            <li>All fields marked with * are required</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default AddCashSmall;
