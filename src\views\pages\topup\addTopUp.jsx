import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { api_path } from '../../../../environments';
import { toast } from 'react-toastify';
import { useLocation , useNavigate} from 'react-router-dom';
import useFetch from '../../../hooks/useFetch';
import { UploadOutlined } from '@ant-design/icons';
import { Button, Upload } from 'antd';
import { Select } from 'antd';
import { PageHeader } from '../../../components/ModernBreadcrumb';


const AddTopUp = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const id = queryParams.get('id');
  const isEdit = id !== null;

  const navigate = useNavigate()
  
  const { data: allPhone , loading : phoneLoading } = useFetch('other/allPhone');

  
  const { data: topup, loading, error: cashError } = useFetch(isEdit ? 'other/singleTopup/' + id : '');
  
  const [customer, setCustomer] = useState('');
  const [phone, setPhone] = useState('');
  const [CPphoneName, setCPPhoneName] = useState('');
  const [CPimei1, setCPImei1] = useState('');
  const [CPimei2, setCPImei2] = useState('');
  const [PhoneID, setPhoneID] = useState('');
  const [NIDA, setNIDA] = useState('');
  const [cost, setCost] = useState('');
  const [closerUser, setCloserUser] = useState('');
  const [image, setImage] = useState('');

  const structuredPhones = allPhone?.map(phone => ({
    value: phone.PhoneID,  
    label: phone.PhoneName + ' - ' + phone.IMEI1.slice(-9),
  }));
  
  useEffect(() => {
    if (topup) {
      setCustomer(topup.CustomerName);
      setPhone(topup.PhoneNumber);
      setCPPhoneName(topup.CustomerPhone);
      setCPImei1(topup.CPIMEI1);
      setCPImei2(topup.CPIMEI2);
      setPhoneID(topup.PhoneID)
      setNIDA(topup.CustomerNIDA)
      setCost(topup.TopUpCost)
      setCloserUser(topup.CloserUserPhone)
      
    }
  }, [topup]);

  const [error, setError] = useState('');

  const props = {
    action: `${api_path}/api/other/addTopup`,  
    listType: 'picture',
    beforeUpload: (file) => {
      setImage(file);  
      return false;    
    },
    onRemove: () => {
      setImage(null);  
    }
  };

  const handleAdd = async (e) => {
    e.preventDefault();
  
    setError('');  // Clear any previous errors
  
    if (!customer || !phone || !CPphoneName || !CPimei1 || !cost || !closerUser || !image) {
      setError('Please fill all required fields, including image upload.');
      return;
    }
  
    const token = localStorage.getItem('joh_token');
    if (!token) {
      throw new Error('No token found');
    }
  
    const topupData = {
      customer,
      phone,
      CPphoneName,
      CPimei1,
      CPimei2,
      PhoneID,
      NIDA,
      closerUser,
      cost,
      image
    };
  
    try {
      let response;
      
      // Create FormData object to send the image with other data
      const formData = new FormData();
      formData.append('customer', customer);
      formData.append('phone', phone);
      formData.append('CPphoneName', CPphoneName);
      formData.append('CPimei1', CPimei1);
      formData.append('CPimei2', CPimei2);
      formData.append('PhoneID', PhoneID);
      formData.append('NIDA', NIDA);
      formData.append('closerUser', closerUser);
      formData.append('cost', cost);
      formData.append('image', image); 
  
      // If we're editing, use PUT request, otherwise use POST
      if (isEdit) {
        response = await axios.put(`${api_path}/api/other/updateTopup/${id}`, formData, {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'multipart/form-data'  // Important for sending files
          }
        });
      } else {
        response = await axios.post(`${api_path}/api/other/addTopup`, formData, {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'multipart/form-data'  // Important for sending files
          }
        });
      }
  
      if (response.status === 200) {
        toast.success(`${isEdit ? 'Top Up Updated' : 'Top Up Added'} successfully`);
        // Reset the form fields after success
        setCustomer('');
        setPhone('');
        setCPPhoneName('');
        setCPImei1('');
        setCPImei2('');
        setPhoneID('');
        setNIDA('');
        setCost('');
        setCloserUser('');
        setImage(null);  // Clear image
        navigate('/view-topups');
      }
    } catch (error) {
      console.error(error);
  
      if (error.response) {
        setError(error.response.data.message || 'An error occurred. Please try again.');
      } else if (error.request) {
        setError('Network error. Please check your connection.');
      } else {
        setError('An unexpected error occurred.');
      }
  
      toast.error(error.message || 'Failed to add return');
    }
  };

  if (loading || phoneLoading) return (
    <div style={{ maxWidth: '1000px', margin: '0 auto' }}>
      <div className="modern-card">
        <div className="modern-card-body">
          <div className="text-center py-8">
            <div className="loading-spinner" style={{ width: '40px', height: '40px', margin: '0 auto 16px' }}></div>
            <h5 className="text-gray-600 mb-2">Loading...</h5>
            <p className="text-gray-500">
              {loading ? 'Loading top-up data...' : 'Loading available phones...'}
            </p>
          </div>

          {/* Skeleton Loading */}
          <div className="mt-6">
            <div className="skeleton-loader" style={{ height: '20px', marginBottom: '16px' }}></div>
            <div className="skeleton-loader" style={{ height: '48px', marginBottom: '24px' }}></div>
            <div className="skeleton-loader" style={{ height: '48px', marginBottom: '24px' }}></div>
            <div className="skeleton-loader" style={{ height: '48px', marginBottom: '24px' }}></div>
          </div>
        </div>
      </div>
    </div>
  );

  if (cashError) return (
    <div className="modern-alert alert-error">
      <strong>Error:</strong> Failed to load top-up data - {cashError.message}
    </div>
  );

  const handleSelectChange = (value) => {
    setPhoneID(value);
  };

  return (
    <div style={{ maxWidth: '1000px', margin: '0 auto' }}>
      {/* Modern Header */}
      <PageHeader
        title={`${isEdit ? 'Edit' : 'Add'} Phone Top-Up`}
        subtitle={isEdit ? 'Update the phone top-up details' : 'Create a new phone top-up record'}
        breadcrumbs={[
          { title: 'Dashboard', path: '/', icon: '🏠' },
          { title: 'Top-Ups', path: '/view-topups', icon: '📱' },
          { title: isEdit ? 'Edit Top-Up' : 'New Top-Up', path: '/add-topup', isActive: true }
        ]}
      />

      {/* Modern Form Card */}
      <div className="modern-card">
        <div className="modern-card-body">
          <form onSubmit={handleAdd}>
            {error && (
              <div className="modern-alert alert-error mb-6">
                <strong>Error:</strong> {error}
              </div>
            )}

            {/* Customer Information Section */}
            <div className="mb-8">
              <h5 className="text-gray-800 mb-4 d-flex align-items-center gap-2">
                👤 Customer Information
              </h5>
              <div className="row">
                <div className="col-md-6">
                  <div className="modern-form-group">
                    <label className="modern-form-label">
                      Customer Name *
                    </label>
                    <input
                      type="text"
                      className="modern-form-input"
                      placeholder="Enter customer name"
                      value={customer}
                      onChange={(e) => setCustomer(e.target.value)}
                      required
                    />
                  </div>
                </div>

                <div className="col-md-6">
                  <div className="modern-form-group">
                    <label className="modern-form-label">
                      Phone Number *
                    </label>
                    <input
                      type="tel"
                      className="modern-form-input"
                      placeholder="Enter phone number"
                      value={phone}
                      onChange={(e) => setPhone(e.target.value)}
                      required
                    />
                  </div>
                </div>
              </div>

              <div className="modern-form-group">
                <label className="modern-form-label">
                  NIDA Number
                </label>
                <input
                  type="text"
                  className="modern-form-input"
                  placeholder="Enter NIDA number"
                  value={NIDA}
                  onChange={(e) => setNIDA(e.target.value)}
                />
              </div>
            </div>

            {/* Customer Phone Section */}
            <div className="mb-8">
              <h5 className="text-gray-800 mb-4 d-flex align-items-center gap-2">
                📱 Customer's Current Phone
              </h5>
              <div className="row">
                <div className="col-md-4">
                  <div className="modern-form-group">
                    <label className="modern-form-label">
                      Phone Model *
                    </label>
                    <input
                      type="text"
                      className="modern-form-input"
                      placeholder="Enter phone model"
                      value={CPphoneName}
                      onChange={(e) => setCPPhoneName(e.target.value)}
                      required
                    />
                  </div>
                </div>

                <div className="col-md-4">
                  <div className="modern-form-group">
                    <label className="modern-form-label">
                      IMEI 1 *
                    </label>
                    <input
                      type="text"
                      className="modern-form-input"
                      placeholder="Enter IMEI 1"
                      value={CPimei1}
                      onChange={(e) => setCPImei1(e.target.value)}
                      required
                    />
                  </div>
                </div>

                <div className="col-md-4">
                  <div className="modern-form-group">
                    <label className="modern-form-label">
                      IMEI 2
                    </label>
                    <input
                      type="text"
                      className="modern-form-input"
                      placeholder="Enter IMEI 2 (optional)"
                      value={CPimei2}
                      onChange={(e) => setCPImei2(e.target.value)}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* New Phone Selection Section */}
            <div className="mb-8">
              <h5 className="text-gray-800 mb-4 d-flex align-items-center gap-2">
                🔄 New Phone Selection
              </h5>
              <div className="modern-form-group">
                <label className="modern-form-label">
                  Select New Phone *
                </label>
                <Select
                  showSearch
                  placeholder="🔍 Search and select a phone"
                  style={{
                    width: '100%',
                    height: '48px'
                  }}
                  size="large"
                  filterOption={(input, option) =>
                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                  options={structuredPhones}
                  onChange={handleSelectChange}
                  value={PhoneID || undefined}
                  notFoundContent={
                    phoneLoading ? (
                      <div className="text-center py-3">
                        <div className="loading-spinner"></div>
                        <span className="ml-2">Loading phones...</span>
                      </div>
                    ) : (
                      <div className="text-center py-3 text-gray-500">
                        No phones found
                      </div>
                    )
                  }
                />
                <small className="text-gray-500 mt-1 d-block">
                  Select the phone the customer will receive
                </small>
              </div>
            </div>

            {/* Transaction Details Section */}
            <div className="mb-8">
              <h5 className="text-gray-800 mb-4 d-flex align-items-center gap-2">
                💰 Transaction Details
              </h5>
              <div className="row">
                <div className="col-md-6">
                  <div className="modern-form-group">
                    <label className="modern-form-label">
                      Top-Up Amount (TSH) *
                    </label>
                    <input
                      type="number"
                      className="modern-form-input"
                      placeholder="Enter top-up amount"
                      value={cost}
                      onChange={(e) => setCost(e.target.value)}
                      min="0"
                      step="1000"
                      required
                    />
                    <small className="text-gray-500 mt-1 d-block">
                      Amount customer pays for the upgrade
                    </small>
                  </div>
                </div>

                <div className="col-md-6">
                  <div className="modern-form-group">
                    <label className="modern-form-label">
                      Emergency Contact *
                    </label>
                    <input
                      type="tel"
                      className="modern-form-input"
                      placeholder="Enter emergency contact number"
                      value={closerUser}
                      onChange={(e) => setCloserUser(e.target.value)}
                      required
                    />
                    <small className="text-gray-500 mt-1 d-block">
                      Close relative or friend's phone number
                    </small>
                  </div>
                </div>
              </div>
            </div>

            {/* Document Upload Section */}
            <div className="mb-8">
              <h5 className="text-gray-800 mb-4 d-flex align-items-center gap-2">
                📄 Document Upload
              </h5>
              <div className="modern-form-group">
                <label className="modern-form-label">
                  NIDA Photo *
                </label>
                <div className="upload-area" style={{
                  border: '2px dashed var(--gray-300)',
                  borderRadius: '8px',
                  padding: '24px',
                  textAlign: 'center',
                  background: 'var(--gray-50)'
                }}>
                  <Upload {...props}>
                    <Button
                      icon={<UploadOutlined />}
                      size="large"
                      style={{
                        background: 'var(--primary-500)',
                        borderColor: 'var(--primary-500)',
                        color: 'white'
                      }}
                    >
                      Upload NIDA Photo
                    </Button>
                  </Upload>
                  <p className="text-gray-500 mt-2 mb-0">
                    Upload a clear photo of the customer's NIDA card
                  </p>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="d-flex gap-3 justify-content-end mt-6">
              <button
                type="button"
                className="btn-modern btn-secondary-modern"
                onClick={() => window.history.back()}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="btn-modern btn-primary-modern"
                disabled={!customer || !phone || !CPphoneName || !CPimei1 || !PhoneID || !cost || !closerUser || !image}
              >
                {isEdit ? '✅ Update Top-Up' : '🔄 Process Top-Up'}
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Help Section */}
      <div className="modern-card mt-4" style={{ background: 'var(--warning-50)' }}>
        <div className="modern-card-body">
          <h5 className="text-warning-600 mb-3">⚠️ Important Notes</h5>
          <ul className="text-sm text-gray-600 mb-0" style={{ paddingLeft: '20px' }}>
            <li>Ensure all customer information is accurate before processing</li>
            <li>Verify the IMEI numbers of the customer's current phone</li>
            <li>Upload a clear photo of the customer's NIDA card</li>
            <li>Double-check the selected new phone and top-up amount</li>
            <li>Emergency contact is required for verification purposes</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default AddTopUp;
