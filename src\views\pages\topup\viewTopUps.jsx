import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';

import CIcon from '@coreui/icons-react';
import * as icon from '@coreui/icons';

import useFetch from '../../../hooks/useFetch';
import { api_path } from '../../../../environments';
import { toast } from 'react-toastify';
import axios from 'axios';
import { useAuth } from '../../../hooks/AuthContext'
import { DatePicker , Tooltip } from 'antd';
const { RangePicker } = DatePicker;
import { jsPDF } from "jspdf";
import { useMediaQuery } from 'react-responsive';

import { FaFilePdf } from "react-icons/fa6";
import { IoAddCircleSharp } from "react-icons/io5";
import { PageHeader } from '../../../components/ModernBreadcrumb';
import AdvancedSearch from '../../../components/AdvancedSearch';

const ViewTopUps = () => {
  const { role } = useAuth()
  const isAdmin = role == 'admin';

  const isMobile = useMediaQuery({ query: '(max-width:767px)' })

  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10000; 

  const { data: allTopups , loading , error} = useFetch('other/allTopups');

  const initialCashRecords = Array.isArray(allTopups)
  ? allTopups.map(topup => ({
      id: topup.TopUpID,
      customer: topup.CustomerName,
      phone: topup.PhoneNumber, 
      CPpName: topup.CustomerPhone,
      CPimei1: topup.CPIMEI1,
      NPimei1: topup.NPimei1,
      CPimei2: topup.CPIMEI2,
      NPpName: topup.PhoneName,
      cost: topup.TopUpCost,
      NIDA: topup.CustomerNIDA,
      agent: topup.UserName,
      date: topup.created_at
  }))
  : [];

  
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      year: 'numeric',
      month: 'numeric',
      day: 'numeric',
    });
  };

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'Tsh' }).format(value);
  }

  const filteredStaffs = initialCashRecords.filter(topup =>
    topup.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
    topup.phone.toLowerCase().includes(searchTerm.toLowerCase()) ||
    topup.NPpName.includes(searchTerm) ||
    topup.NPimei1.includes(searchTerm) ||
    topup.CPimei1.includes(searchTerm)
  );

  const totalPages = Math.ceil(filteredStaffs.length / itemsPerPage);
  
  const indexOfLastStaff = currentPage * itemsPerPage;
  const indexOfFirstStaff = indexOfLastStaff - itemsPerPage;
  const currentStaffs = filteredStaffs.slice(indexOfFirstStaff, indexOfLastStaff);

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };


  const handleDelete =async (topupID) => {
    const response = await axios.post(`${api_path}/api/other/deleteTopup/${topupID}`);

    if(response.status == 200){
      toast.success("Record Deleted successfully");
      window.location.reload();
    }
  }

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error loading users: {error.message}</div>;

  const exportToPDF = () => {
    const doc = new jsPDF();
    const pageWidth = doc.internal.pageSize.width;
    const pageHeight = doc.internal.pageSize.height;
    const margin = 10;
    const cellPadding = 2;
    const xStart = margin;
    const title = "Smart Phones - TopUp Records";
  
    // Define headers and data
    const headers = ["Date","Customer", "Simu ya Mteja", "Aliyochukua", "Aliyoongezea","Agent"];
    const dataForPdf = currentStaffs.map(cash => [
      cash.date ? formatDate(cash.date) : '',
      cash.customer || '',
      cash.CPpName + ' - ' + cash.CPimei1.slice(-9) || '',
      cash.NPpName + ' - ' + cash.NPimei1.slice(-9) || '',
      cash.cost ? formatCurrency(cash.cost) : '',
      cash.agent || ''
    ]);

    // Calculate column widths
    const columnWidths = headers.map((header, index) => {
    const headerWidth = doc.getStringUnitWidth(header) * doc.internal.getFontSize();
    const maxDataWidth = Math.max(...dataForPdf.map(row => {
      const cellValue = row[index] || ''; // Handle undefined/null values
      return doc.getStringUnitWidth(String(cellValue)) * doc.internal.getFontSize();
    }));
    return Math.max(headerWidth, maxDataWidth) + 2 * cellPadding;
  });
  
    // Adjust columns if they exceed page width
    const totalTableWidth = columnWidths.reduce((sum, width) => sum + width, 0);
    if (totalTableWidth > (pageWidth - 2 * margin)) {
      const scaleFactor = (pageWidth - 2 * margin) / totalTableWidth;
      columnWidths.forEach((_, i) => columnWidths[i] *= scaleFactor);
    }
  
    // Draw title and headers on current page
    const drawTitleAndHeaders = () => {
      doc.setFontSize(16);
      doc.setFont("helvetica", "bold");
      doc.text(title, pageWidth / 2, 20, { align: "center" });
  
      doc.setFontSize(9);
      headers.forEach((header, i) => {
        const xPosition = xStart + columnWidths.slice(0, i).reduce((sum, w) => sum + w, 0) + cellPadding;
        doc.text(header, xPosition, 35); // Header text at Y=35
      });
  
      headers.forEach((_, i) => {
        const xPosition = xStart + columnWidths.slice(0, i).reduce((sum, w) => sum + w, 0);
        doc.rect(xPosition, 30, columnWidths[i], 10); // Header background
      });
    };
  
    // Initial page setup
    let currentPage = doc;
    let currentY = 40; // Start Y for data rows
    const cellHeight = 8;
    const maxY = pageHeight - 20; // Bottom margin
    let rowsOnCurrentPage = 0;
  
    // Draw initial headers
    drawTitleAndHeaders();
  
    for (let rowIndex = 0; rowIndex < dataForPdf.length; rowIndex++) {
      const row = dataForPdf[rowIndex];
  
      // Check if we need a new page
      if (currentY + (rowsOnCurrentPage + 1) * cellHeight > maxY) {
        currentPage.addPage();
        currentPage = currentPage;
        currentY = 40;
        rowsOnCurrentPage = 0;
        drawTitleAndHeaders();
      }
  
      // Draw row cells
      row.forEach((col, colIndex) => {
        const xPosition = xStart + columnWidths.slice(0, colIndex).reduce((sum, w) => sum + w, 0);
        const yPosition = currentY + (rowsOnCurrentPage * cellHeight);
  
        // Draw cell border
        doc.rect(xPosition, yPosition, columnWidths[colIndex], cellHeight);
  
        // Set font style and size
        doc.setFont("helvetica", "normal");
        let fontSize = 8;
        const availableWidth = columnWidths[colIndex] - 2 * cellPadding;
        let textWidth = doc.getStringUnitWidth(col.toString()) * fontSize;
        
        // Adjust font size if needed
        while (textWidth > availableWidth && fontSize > 6) {
          fontSize -= 0.5;
          textWidth = doc.getStringUnitWidth(col.toString()) * fontSize;
        }
        doc.setFontSize(fontSize);
  
        // Draw cell content
        doc.text(col.toString(), xPosition + cellPadding, yPosition + cellHeight - 2);
      });
  
      rowsOnCurrentPage++;
    }
  
    doc.save("Topup_records.pdf");
  };

  return (
    <>
      {/* Modern Page Header with Breadcrumbs */}
      <PageHeader
        title="Top-Up Records"
        subtitle="Manage and track all phone top-up transactions"
        breadcrumbs={[
          { title: 'Dashboard', path: '/', icon: '🏠' },
          { title: 'Top-Ups', path: '/view-topups', isActive: true }
        ]}
        actions={
          <div className="d-flex gap-3 align-items-center">
            {isAdmin && (
              <Tooltip title="Export To PDF">
                <button className="btn-modern btn-success-modern" onClick={exportToPDF}>
                  <FaFilePdf /> Export PDF
                </button>
              </Tooltip>
            )}
            <Link to="/add-topup">
              <Tooltip title="Add New Top-Up">
                <button className="btn-modern btn-primary-modern">
                  <IoAddCircleSharp /> New Top-Up
                </button>
              </Tooltip>
            </Link>
          </div>
        }
      />

      {/* Advanced Search Component */}
      <AdvancedSearch
        data={initialCashRecords}
        onFilteredDataChange={(filtered) => {
          setCurrentPage(1);
        }}
        searchFields={['customer', 'phone', 'CPpName', 'NPpName', 'CPimei1', 'NPimei1', 'agent']}
        filterFields={[
          { key: 'agent', label: 'Agent', multiple: false },
          { key: 'CPpName', label: 'Customer Phone', multiple: false },
          { key: 'NPpName', label: 'New Phone', multiple: false }
        ]}
        exportOptions={{
          enabled: isAdmin,
          onExport: (data, format) => {
            if (format === 'pdf') {
              exportToPDF();
            }
          }
        }}
        placeholder="🔍 Search by customer, phone models, or IMEI..."
        className="mb-6"
      />

      {/* Modern Table Section */}
      <div className="modern-card">
        <div className="modern-card-header">
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h4 className="mb-1">Top-Up Records</h4>
              <p className="text-gray-500 mb-0" style={{ fontSize: '14px' }}>
                Showing {filteredStaffs.length} of {initialCashRecords.length} records
              </p>
            </div>
            <div className="d-flex gap-2 align-items-center">
              <span className="badge" style={{
                background: 'var(--warning-100)',
                color: 'var(--warning-700)',
                padding: '4px 12px',
                borderRadius: '20px',
                fontSize: '12px',
                fontWeight: '500'
              }}>
                {filteredStaffs.length} top-ups
              </span>
            </div>
          </div>
        </div>
        <div className="modern-card-body" style={{ padding: '0' }}>
          <div className="table-responsive">
            <table className="modern-table w-100">
              <thead>
                <tr>
                  <th style={{ width: '60px' }}>#</th>
                  <th>Customer</th>
                  <th>Customer Phone</th>
                  <th>New Phone</th>
                  <th>Top-Up Cost</th>
                  <th className="mobile-hidden">Date</th>
                  {isAdmin && <th className="mobile-hidden">Agent</th>}
                  <th style={{ width: '120px' }}>Actions</th>
                </tr>
              </thead>
              <tbody>
                {currentStaffs.length === 0 ? (
                  <tr>
                    <td colSpan={isAdmin ? 8 : 7} className="text-center py-8">
                      <div style={{ padding: '40px 20px' }}>
                        <div style={{ fontSize: '48px', marginBottom: '16px' }}>📱</div>
                        <h5 className="text-gray-600 mb-2">No top-up records found</h5>
                        <p className="text-gray-500">
                          {searchTerm
                            ? 'Try adjusting your search criteria'
                            : 'No top-up records available yet'}
                        </p>
                      </div>
                    </td>
                  </tr>
                ) : (
                  currentStaffs.map((topup, index) => (
                    <tr key={topup.id}>
                      <td>
                        <span className="font-medium text-gray-700">
                          {(currentPage - 1) * itemsPerPage + index + 1}
                        </span>
                      </td>
                      <td>
                        <div>
                          <div className="font-medium text-gray-900">
                            {topup.customer}
                          </div>
                          <div className="text-sm text-gray-500">
                            {topup.phone}
                          </div>
                        </div>
                      </td>
                      <td>
                        <div className="d-flex align-items-center gap-2">
                          <div style={{
                            width: '8px',
                            height: '8px',
                            borderRadius: '50%',
                            background: 'var(--error-500)'
                          }}></div>
                          <span className="font-medium">
                            {topup.CPpName}
                          </span>
                        </div>
                        {topup.CPimei1 && (
                          <div className="text-xs text-gray-500 mt-1">
                            IMEI: {topup.CPimei1.slice(-9)}
                          </div>
                        )}
                      </td>
                      <td>
                        <div className="d-flex align-items-center gap-2">
                          <div style={{
                            width: '8px',
                            height: '8px',
                            borderRadius: '50%',
                            background: 'var(--success-500)'
                          }}></div>
                          <span className="font-medium">
                            {topup.NPpName}
                          </span>
                        </div>
                        {topup.NPimei1 && (
                          <div className="text-xs text-gray-500 mt-1">
                            IMEI: {topup.NPimei1.slice(-9)}
                          </div>
                        )}
                      </td>
                      <td>
                        <span className="font-semibold" style={{ color: 'var(--warning-600)' }}>
                          {formatCurrency(topup.cost)}
                        </span>
                      </td>
                      <td className="mobile-hidden">
                        <span className="text-sm text-gray-600">
                          {topup.date ? formatDate(topup.date) : 'N/A'}
                        </span>
                      </td>
                      {isAdmin && (
                        <td className="mobile-hidden">
                          <span className="text-sm font-medium text-gray-700">
                            {topup.agent}
                          </span>
                        </td>
                      )}
                      <td>
                        <div className="d-flex gap-1">
                          <Link to={'/single-record?type=topup&id=' + topup.id}>
                            <Tooltip title="View Details">
                              <button className="btn-modern" style={{
                                padding: '6px 8px',
                                background: 'var(--gray-100)',
                                border: '1px solid var(--gray-300)',
                                borderRadius: '6px',
                                color: 'var(--gray-600)'
                              }}>
                                <CIcon icon={icon.cilChevronDoubleRight} size="sm" />
                              </button>
                            </Tooltip>
                          </Link>

                          {isAdmin && (
                            <>
                              <Link to={'/add-topup?id=' + topup.id}>
                                <Tooltip title="Edit Record">
                                  <button className="btn-modern" style={{
                                    padding: '6px 8px',
                                    background: 'var(--primary-100)',
                                    border: '1px solid var(--primary-300)',
                                    borderRadius: '6px',
                                    color: 'var(--primary-600)'
                                  }}>
                                    <CIcon icon={icon.cilPenAlt} size="sm" />
                                  </button>
                                </Tooltip>
                              </Link>
                              <Tooltip title="Delete Record">
                                <button
                                  className="btn-modern"
                                  style={{
                                    padding: '6px 8px',
                                    background: 'var(--error-100)',
                                    border: '1px solid var(--error-300)',
                                    borderRadius: '6px',
                                    color: 'var(--error-600)'
                                  }}
                                  onClick={() => {
                                    const isConfirmed = window.confirm("Are you sure you want to delete this record?");
                                    if (isConfirmed) {
                                      handleDelete(topup.id);
                                    }
                                  }}
                                >
                                  <CIcon icon={icon.cilTrash} size="sm" />
                                </button>
                              </Tooltip>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </>
  );
};

export default ViewTopUps;
