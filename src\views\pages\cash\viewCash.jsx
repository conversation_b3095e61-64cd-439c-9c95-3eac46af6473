import React, { useState } from 'react';
import {
  CCard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CButton,
  CFormInput,
  CPagination,
  CPaginationItem
} from '@coreui/react';
import { Link } from 'react-router-dom';
import { FaFilePdf } from "react-icons/fa6";
import { IoAddCircleSharp } from "react-icons/io5";

import CIcon from '@coreui/icons-react';
import * as icon from '@coreui/icons';

import useFetch from '../../../hooks/useFetch';
import { api_path } from '../../../../environments';
import { toast } from 'react-toastify';
import axios from 'axios';
import { useAuth } from '../../../hooks/AuthContext';
import { DatePicker , Tooltip } from 'antd';
import { jsPDF } from "jspdf";
import { useMediaQuery } from 'react-responsive';
import dayjs from 'dayjs';

const ViewCash = () => {
  const { role } = useAuth();
  const isAdmin = role === 'admin';

  const isMobile = useMediaQuery({ query: '(max-width:767px)' })

  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10000;
  const [selectedDate, setSelectedDate] = useState();

  const { data: allCash, loading, error } = useFetch('other/allCash');

  const initialCashRecords = Array.isArray(allCash)
    ? allCash.map(cash => ({
        id: cash.CSID,
        customer: cash.CustomerName,
        phone: cash.CustomerPhoneNumber,
        imei1: cash.IMEI1,
        imei2: cash.IMEI2,
        price: cash.SellingPrice,
        agent: cash.UserName,
        pName: cash.PhoneName,
        code: cash.code,
        date: cash.created_at
      }))
    : [];

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'Tsh' }).format(value);
  };

  const filteredStaffs = initialCashRecords.filter(staff => {
    // Text search filter
    const matchesSearch = !searchTerm ||
      (staff.customer && staff.customer.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (staff.phone && staff.phone.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (staff.pName && staff.pName.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (staff.code && staff.code.includes(searchTerm)) ||
      (staff.imei1 && staff.imei1.includes(searchTerm)) ||
      (staff.imei2 && staff.imei2.includes(searchTerm));

    // Date filter
    const matchesDate = !selectedDate ||
      (staff.date && dayjs(staff.date).format('YYYY-MM-DD') === selectedDate);

    return matchesSearch && matchesDate;
  });

  const totalPages = Math.ceil(filteredStaffs.length / itemsPerPage);

  const indexOfLastStaff = currentPage * itemsPerPage;
  const indexOfFirstStaff = indexOfLastStaff - itemsPerPage;
  const currentStaffs = filteredStaffs.slice(indexOfFirstStaff, indexOfLastStaff);

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const handleDeleteCash = async (cashID) => {
    const response = await axios.post(`${api_path}/api/other/deleteCash/${cashID}`);
    if (response.status === 200) {
      toast.success('User Deleted successfully');
      window.location.reload();
    }
  };

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error loading cash: {error.message}</div>;


  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      year: 'numeric',
      month: 'numeric',
      day: 'numeric',
    });
  };




  const exportToPDF = () => {
    const doc = new jsPDF();
    const pageWidth = doc.internal.pageSize.width;
    const pageHeight = doc.internal.pageSize.height;
    const margin = 10;
    const cellPadding = 2;
    const xStart = margin;
    const title = "Smart Phones - Cash Records";
  
    // Define headers and data
    const headers = ["Code", "Customer", "Phone", "Phone Name", "Price", "Date", "Agent"];
    const dataForPdf = currentStaffs.map(cash => [
      cash.code || '',
      cash.customer || '',
      cash.phone || '',
      cash.pName || '',
      cash.price ? formatCurrency(cash.price) : '',
      cash.date ? formatDate(cash.date) : '',
      cash.agent || ''
    ]);

    // Calculate column widths
    const columnWidths = headers.map((header, index) => {
    const headerWidth = doc.getStringUnitWidth(header) * doc.internal.getFontSize();
    const maxDataWidth = Math.max(...dataForPdf.map(row => {
      const cellValue = row[index] || ''; // Handle undefined/null values
      return doc.getStringUnitWidth(String(cellValue)) * doc.internal.getFontSize();
    }));
    return Math.max(headerWidth, maxDataWidth) + 2 * cellPadding;
  });
  
    // Adjust columns if they exceed page width
    const totalTableWidth = columnWidths.reduce((sum, width) => sum + width, 0);
    if (totalTableWidth > (pageWidth - 2 * margin)) {
      const scaleFactor = (pageWidth - 2 * margin) / totalTableWidth;
      columnWidths.forEach((_, i) => columnWidths[i] *= scaleFactor);
    }
  
    // Draw title and headers on current page
    const drawTitleAndHeaders = () => {
      doc.setFontSize(16);
      doc.setFont("helvetica", "bold");
      doc.text(title, pageWidth / 2, 20, { align: "center" });
  
      doc.setFontSize(9);
      headers.forEach((header, i) => {
        const xPosition = xStart + columnWidths.slice(0, i).reduce((sum, w) => sum + w, 0) + cellPadding;
        doc.text(header, xPosition, 35); // Header text at Y=35
      });
  
      headers.forEach((_, i) => {
        const xPosition = xStart + columnWidths.slice(0, i).reduce((sum, w) => sum + w, 0);
        doc.rect(xPosition, 30, columnWidths[i], 10); // Header background
      });
    };
  
    // Initial page setup
    let currentPage = doc;
    let currentY = 40; // Start Y for data rows
    const cellHeight = 8;
    const maxY = pageHeight - 20; // Bottom margin
    let rowsOnCurrentPage = 0;
  
    // Draw initial headers
    drawTitleAndHeaders();
  
    for (let rowIndex = 0; rowIndex < dataForPdf.length; rowIndex++) {
      const row = dataForPdf[rowIndex];
  
      // Check if we need a new page
      if (currentY + (rowsOnCurrentPage + 1) * cellHeight > maxY) {
        currentPage.addPage();
        currentPage = currentPage;
        currentY = 40;
        rowsOnCurrentPage = 0;
        drawTitleAndHeaders();
      }
  
      // Draw row cells
      row.forEach((col, colIndex) => {
        const xPosition = xStart + columnWidths.slice(0, colIndex).reduce((sum, w) => sum + w, 0);
        const yPosition = currentY + (rowsOnCurrentPage * cellHeight);
  
        // Draw cell border
        doc.rect(xPosition, yPosition, columnWidths[colIndex], cellHeight);
  
        // Set font style and size
        doc.setFont("helvetica", "normal");
        let fontSize = 8;
        const availableWidth = columnWidths[colIndex] - 2 * cellPadding;
        let textWidth = doc.getStringUnitWidth(col.toString()) * fontSize;
        
        // Adjust font size if needed
        while (textWidth > availableWidth && fontSize > 6) {
          fontSize -= 0.5;
          textWidth = doc.getStringUnitWidth(col.toString()) * fontSize;
        }
        doc.setFontSize(fontSize);
  
        // Draw cell content
        doc.text(col.toString(), xPosition + cellPadding, yPosition + cellHeight - 2);
      });
  
      rowsOnCurrentPage++;
    }
  
    doc.save("smart_phones_cash_records.pdf");
  };
  
  const onDateChange = (date, dateString) => {
    setSelectedDate(dateString);
  };
  
  return (
    <>
    <div className="d-flex justify-content-between mb-2">
        <div className=""></div>
        <div
          className={`${isMobile ? 'flex-column' : 'd-flex'}`}
          style={{ display: 'flex' }}
        >
          <div className={`d-flex ${isMobile ? 'mb-3 justify-content-end' : 'flex-row'}`}>

            {/* Button for exporting PDF */}
            {isAdmin && (

              <Tooltip title="Export To PDF">
                <CButton color="success" className="text-white" onClick={exportToPDF}>
                    <FaFilePdf />
                </CButton>
              </Tooltip>
            )}

            <Link to="/add-cash" className="mx-3">
              <Tooltip title="Add New Record">
                <CButton color="primary">
                  <IoAddCircleSharp />
                </CButton>
              </Tooltip>
            </Link>

          </div>

          {/* Date filter */}
          <div className="d-flex gap-2 align-items-center">
             <DatePicker
                placeholder="Filter by date"
                onChange={onDateChange}
                value={selectedDate ? dayjs(selectedDate) : null}
                allowClear
              />
              {selectedDate && (
                <CButton
                  color="secondary"
                  size="sm"
                  onClick={() => setSelectedDate(null)}
                  title="Clear date filter"
                >
                  Clear
                </CButton>
              )}
          </div>
        </div>
      </div>

    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <div className="d-flex justify-content-between align-items-center">
              <strong>All Cash Sales Records</strong>
              <small className="text-muted">
                Showing {filteredStaffs.length} of {initialCashRecords.length} records
                {selectedDate && ` (filtered by date: ${selectedDate})`}
              </small>
            </div>
            <div className='d-flex gap-2'>
              <CFormInput
                type="text"
                placeholder="Search by customer, phone, model, code, or IMEI..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="mt-2"
              />
            </div>
          </CCardHeader>
          <CCardBody>
            <CTable responsive>
              <CTableHead>
                <CTableRow>
                  <CTableHeaderCell scope="col">#</CTableHeaderCell>
                  <CTableHeaderCell scope="col">Customer</CTableHeaderCell>
                  <CTableHeaderCell scope="col">Phone Model</CTableHeaderCell>
                  <CTableHeaderCell scope="col">Price</CTableHeaderCell>
                  <CTableHeaderCell scope="col">Code</CTableHeaderCell>
                  <CTableHeaderCell scope="col">Date</CTableHeaderCell>
                  {isAdmin && (
                    <>
                    <CTableHeaderCell scope="col">Agent</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Action</CTableHeaderCell>
                    </>
                  )}
                </CTableRow>
              </CTableHead>
              <CTableBody>
                {currentStaffs.map((cash,index) => (
                  <CTableRow key={cash.id}>
                    <CTableHeaderCell scope="row">{index + 1}</CTableHeaderCell>
                    <CTableDataCell>
                      <div>
                        <div>{cash.customer || 'N/A'}</div>
                        <small className="text-muted">{cash.phone}</small>
                      </div>
                    </CTableDataCell>
                    <CTableDataCell>
                      <div>
                        <div>{cash.pName || 'N/A'}</div>
                        {cash.imei1 && (
                          <small className="text-muted">IMEI: {cash.imei1}</small>
                        )}
                      </div>
                    </CTableDataCell>
                    <CTableDataCell>{formatCurrency(cash.price)}</CTableDataCell>
                    <CTableDataCell>{cash.code}</CTableDataCell>
                    <CTableDataCell>{cash.date ? formatDate(cash.date) : 'N/A'}</CTableDataCell>

                    {isAdmin && (
                      <>
                      <CTableDataCell>{cash.agent}</CTableDataCell>

                      <CTableDataCell className='d-flex gap-2'>
                      <Link to={'/single-record?type=cash&id=' + cash.id}>
                        <CButton color="secondary">
                          <CIcon icon={icon.cilChevronDoubleRight} />
                        </CButton>
                        </Link>
                        <Link to={'/add-cash?id=' + cash.id}>
                        <CButton color="primary">
                          <CIcon icon={icon.cilPenAlt} />
                        </CButton>
                        </Link>
                        <CButton
                            color="danger"
                            className='text-white'
                            onClick={() => {
                              const isConfirmed = window.confirm("Are you sure you want to delete this record?");

                              if (isConfirmed) {
                                handleDeleteCash(cash.id);
                              }
                            }}
                          >
                          <CIcon icon={icon.cilTrash} />
                        </CButton>
                      </CTableDataCell>
                    </>
                    )}
                  </CTableRow>
                ))}
              </CTableBody>
            </CTable>
            <CPagination aria-label="Page navigation example" align="end">
              <CPaginationItem
                aria-label="Previous"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                >
                <span aria-hidden="true">&laquo;</span>
              </CPaginationItem>
              {Array.from({ length: totalPages }, (_, index) => (
                <CPaginationItem
                  key={index + 1}
                  active={index + 1 === currentPage}
                  onClick={() => handlePageChange(index + 1)}
                  >
                  {index + 1}
                </CPaginationItem>
              ))}
              <CPaginationItem
                aria-label="Next"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                >
                <span aria-hidden="true">&raquo;</span>
              </CPaginationItem>
            </CPagination>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
    </>
  );
};

export default ViewCash;
